#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建跨平台发布包
支持Windows11和Linux，自动Python环境安装
"""

import os
import shutil
import zipfile
from datetime import datetime

def create_cross_platform_release():
    """创建跨平台发布包"""
    print("=" * 60)
    print("        创建跨平台发布包")
    print("=" * 60)
    
    # 创建发布目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    release_name = f"Excel_Converter_CrossPlatform_{timestamp}"
    release_dir = os.path.join("cross_platform_release", release_name)
    
    os.makedirs(release_dir, exist_ok=True)
    print(f"📁 创建发布目录: {release_dir}")
    
    # 复制核心文件
    copy_core_files(release_dir)
    
    # 创建Windows脚本
    create_windows_scripts(release_dir)
    
    # 创建Linux脚本
    create_linux_scripts(release_dir)
    
    # 创建Python安装器
    create_python_installer(release_dir)
    
    # 创建说明文档
    create_documentation(release_dir)
    
    # 创建ZIP包
    zip_path = f"{release_dir}.zip"
    create_zip_package(release_dir, zip_path)
    
    size_mb = os.path.getsize(zip_path) / (1024 * 1024)
    
    print(f"\n🎉 跨平台发布包创建成功!")
    print(f"📦 包文件: {zip_path}")
    print(f"📊 包大小: {size_mb:.2f} MB")
    
    return zip_path

def copy_core_files(release_dir):
    """复制核心文件"""
    print("📁 复制核心文件...")
    
    # 核心文件列表
    core_files = [
        "app.py",
        "requirements.txt", 
        "start_server.py",
        "stop_server.py"
    ]
    
    for file in core_files:
        if os.path.exists(file):
            shutil.copy2(file, os.path.join(release_dir, file))
            print(f"  ✅ {file}")
    
    # 复制目录
    core_dirs = [
        "templates",
        "uploads", 
        "outputs"
    ]
    
    for dir_name in core_dirs:
        if os.path.exists(dir_name):
            dst_dir = os.path.join(release_dir, dir_name)
            if os.path.exists(dst_dir):
                shutil.rmtree(dst_dir)
            shutil.copytree(dir_name, dst_dir)
            print(f"  ✅ {dir_name}/")
        else:
            # 创建空目录
            os.makedirs(os.path.join(release_dir, dir_name), exist_ok=True)
            print(f"  ✅ {dir_name}/ (空目录)")

def create_windows_scripts(release_dir):
    """创建Windows脚本"""
    print("📝 创建Windows脚本...")
    
    # Windows主启动脚本 - 解决所有编码和命令问题
    windows_start = '''@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion
title Excel格式转换工具

echo ========================================
echo     Excel格式转换工具 v2.1
echo ========================================
echo:
echo 正在启动...
echo:

REM 检查Python环境
echo 检查Python环境...
python --version >nul 2>&1
if !errorlevel! neq 0 (
    echo Python未安装，正在自动安装...
    call install_python.bat
    if !errorlevel! neq 0 (
        echo Python安装失败，请手动安装
        pause
        exit /b 1
    )
)

REM 显示Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Python环境正常 (版本: !PYTHON_VERSION!)
echo:

REM 检查必需文件
echo 检查程序文件...
if not exist "app.py" (
    echo 错误: 未找到app.py文件
    echo 请确保在正确目录运行此脚本
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo 错误: 未找到requirements.txt文件
    echo 请确保在正确目录运行此脚本
    pause
    exit /b 1
)

echo 程序文件检查通过
echo:

REM 检查并安装依赖
echo 检查依赖包...
python -c "import flask" >nul 2>&1
if !errorlevel! neq 0 (
    echo 安装依赖包...
    echo 这可能需要几分钟，请耐心等待...
    echo:
    
    pip install -r requirements.txt
    if !errorlevel! neq 0 (
        echo 依赖安装失败，尝试使用镜像源...
        pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if !errorlevel! neq 0 (
            echo 依赖安装失败，请检查网络连接
            pause
            exit /b 1
        )
    )
    echo 依赖包安装完成
) else (
    echo 依赖包已安装
)

echo:
echo ========================================
echo 启动Excel格式转换工具...
echo:
echo 请在浏览器中访问: http://localhost:8080
echo 按 Ctrl+C 停止服务器
echo ========================================
echo:

REM 启动服务器
python start_server.py

echo:
echo 服务器已停止
pause
'''

    # Python自动安装脚本
    python_installer = '''@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion

echo ========================================
echo     Python自动安装程序
echo ========================================
echo:

REM 检查系统架构
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set ARCH=amd64
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    set ARCH=win32
) else (
    set ARCH=amd64
)

echo 检测到系统架构: !ARCH!
echo:

REM 设置Python下载URL
set PYTHON_VERSION=3.11.5
if "!ARCH!"=="amd64" (
    set PYTHON_URL=https://www.python.org/ftp/python/!PYTHON_VERSION!/python-!PYTHON_VERSION!-amd64.exe
    set PYTHON_FILE=python-!PYTHON_VERSION!-amd64.exe
) else (
    set PYTHON_URL=https://www.python.org/ftp/python/!PYTHON_VERSION!/python-!PYTHON_VERSION!.exe
    set PYTHON_FILE=python-!PYTHON_VERSION!.exe
)

echo 准备下载Python !PYTHON_VERSION!...
echo 下载地址: !PYTHON_URL!
echo:

REM 检查是否有下载工具
where curl >nul 2>&1
if !errorlevel! equ 0 (
    echo 使用curl下载Python...
    curl -L -o "!PYTHON_FILE!" "!PYTHON_URL!"
) else (
    where powershell >nul 2>&1
    if !errorlevel! equ 0 (
        echo 使用PowerShell下载Python...
        powershell -Command "Invoke-WebRequest -Uri '!PYTHON_URL!' -OutFile '!PYTHON_FILE!'"
    ) else (
        echo 无法找到下载工具
        echo 请手动下载Python: !PYTHON_URL!
        echo 下载后运行安装程序，务必勾选"Add Python to PATH"
        pause
        exit /b 1
    )
)

if not exist "!PYTHON_FILE!" (
    echo Python下载失败
    echo 请手动下载并安装Python: https://python.org
    pause
    exit /b 1
)

echo Python下载完成，开始安装...
echo:

REM 静默安装Python
"!PYTHON_FILE!" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0

REM 等待安装完成
timeout /t 30 /nobreak >nul

REM 刷新环境变量
call refreshenv.cmd >nul 2>&1

REM 验证安装
python --version >nul 2>&1
if !errorlevel! equ 0 (
    echo Python安装成功！
    del "!PYTHON_FILE!" >nul 2>&1
) else (
    echo Python安装可能失败，请重启电脑后重试
    echo 或手动安装Python: https://python.org
    pause
    exit /b 1
)

echo:
echo Python环境配置完成
'''

    # Windows停止脚本
    windows_stop = '''@echo off
chcp 65001 >nul 2>&1
title 停止Excel格式转换工具

echo ========================================
echo     停止Excel格式转换工具
echo ========================================
echo:

echo 正在停止服务...
python stop_server.py

echo:
echo 服务停止完成
pause
'''

    # 环境测试脚本
    windows_test = '''@echo off
chcp 65001 >nul 2>&1
setlocal enabledelayedexpansion
title 环境测试

echo ========================================
echo     Excel格式转换工具 - 环境测试
echo ========================================
echo:

echo 当前目录: %CD%
echo 系统版本: 
ver
echo:

echo 检查Python...
python --version 2>&1
if !errorlevel! neq 0 (
    echo ❌ Python未安装
    echo 💡 将自动安装Python环境
) else (
    echo ✅ Python已安装
)

echo:
echo 检查pip...
pip --version 2>&1
if !errorlevel! neq 0 (
    echo ❌ pip未安装
) else (
    echo ✅ pip已安装
)

echo:
echo 检查文件...
if exist "app.py" (
    echo ✅ app.py 存在
) else (
    echo ❌ app.py 不存在
)

if exist "requirements.txt" (
    echo ✅ requirements.txt 存在
) else (
    echo ❌ requirements.txt 不存在
)

if exist "start_server.py" (
    echo ✅ start_server.py 存在
) else (
    echo ❌ start_server.py 不存在
)

echo:
echo 检查Flask...
python -c "import flask; print('Flask版本:', flask.__version__)" 2>&1
if !errorlevel! neq 0 (
    echo ❌ Flask未安装
    echo 💡 需要安装依赖包
) else (
    echo ✅ Flask已安装
)

echo:
echo ========================================
echo 测试完成
echo ========================================
echo:
echo 如果Python未安装，启动脚本会自动安装
echo 如果有其他问题，请根据提示解决
echo:
pause
'''

    # 写入Windows脚本文件
    scripts = [
        ("启动工具.bat", windows_start),
        ("install_python.bat", python_installer),
        ("停止工具.bat", windows_stop),
        ("环境测试.bat", windows_test)
    ]
    
    for filename, content in scripts:
        with open(os.path.join(release_dir, filename), 'w', encoding='utf-8-sig') as f:
            f.write(content)
        print(f"  ✅ {filename}")

def create_linux_scripts(release_dir):
    """创建Linux脚本"""
    print("📝 创建Linux脚本...")

    # Linux启动脚本
    linux_start = '''#!/bin/bash
# Excel格式转换工具 - Linux启动脚本

echo "========================================"
echo "     Excel格式转换工具 v2.1"
echo "========================================"
echo
echo "正在启动..."
echo

# 检查Python环境
echo "检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "Python未安装，正在自动安装..."
    ./install_python.sh
    if [ $? -ne 0 ]; then
        echo "Python安装失败，请手动安装"
        read -p "按回车键退出..."
        exit 1
    fi
    PYTHON_CMD="python3"
fi

# 显示Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1)
echo "Python环境正常 ($PYTHON_VERSION)"
echo

# 检查必需文件
echo "检查程序文件..."
if [ ! -f "app.py" ]; then
    echo "错误: 未找到app.py文件"
    echo "请确保在正确目录运行此脚本"
    read -p "按回车键退出..."
    exit 1
fi

if [ ! -f "requirements.txt" ]; then
    echo "错误: 未找到requirements.txt文件"
    echo "请确保在正确目录运行此脚本"
    read -p "按回车键退出..."
    exit 1
fi

echo "程序文件检查通过"
echo

# 检查并安装依赖
echo "检查依赖包..."
$PYTHON_CMD -c "import flask" &> /dev/null
if [ $? -ne 0 ]; then
    echo "安装依赖包..."
    echo "这可能需要几分钟，请耐心等待..."
    echo

    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "依赖安装失败，尝试使用镜像源..."
        $PYTHON_CMD -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if [ $? -ne 0 ]; then
            echo "依赖安装失败，请检查网络连接"
            read -p "按回车键退出..."
            exit 1
        fi
    fi
    echo "依赖包安装完成"
else
    echo "依赖包已安装"
fi

echo
echo "========================================"
echo "启动Excel格式转换工具..."
echo
echo "请在浏览器中访问: http://localhost:8080"
echo "按 Ctrl+C 停止服务器"
echo "========================================"
echo

# 启动服务器
$PYTHON_CMD start_server.py

echo
echo "服务器已停止"
read -p "按回车键退出..."
'''

    # Linux Python安装脚本
    linux_python_installer = '''#!/bin/bash
# Python自动安装脚本 - Linux

echo "========================================"
echo "     Python自动安装程序 - Linux"
echo "========================================"
echo

# 检测Linux发行版
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "无法检测Linux发行版"
    OS="Unknown"
fi

echo "检测到系统: $OS"
echo

# 根据发行版安装Python
case $OS in
    *"Ubuntu"*|*"Debian"*)
        echo "使用apt安装Python..."
        sudo apt update
        sudo apt install -y python3 python3-pip python3-venv
        ;;
    *"CentOS"*|*"Red Hat"*|*"Fedora"*)
        echo "使用yum/dnf安装Python..."
        if command -v dnf &> /dev/null; then
            sudo dnf install -y python3 python3-pip
        else
            sudo yum install -y python3 python3-pip
        fi
        ;;
    *"Arch"*)
        echo "使用pacman安装Python..."
        sudo pacman -S --noconfirm python python-pip
        ;;
    *"openSUSE"*)
        echo "使用zypper安装Python..."
        sudo zypper install -y python3 python3-pip
        ;;
    *)
        echo "不支持的Linux发行版: $OS"
        echo "请手动安装Python 3.7或更高版本"
        echo "安装命令示例:"
        echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "  CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "  Fedora: sudo dnf install python3 python3-pip"
        echo "  Arch: sudo pacman -S python python-pip"
        exit 1
        ;;
esac

# 验证安装
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1)
    echo "Python安装成功: $PYTHON_VERSION"
else
    echo "Python安装失败"
    exit 1
fi

echo
echo "Python环境配置完成"
'''

    # Linux停止脚本
    linux_stop = '''#!/bin/bash
# Excel格式转换工具 - Linux停止脚本

echo "========================================"
echo "     停止Excel格式转换工具"
echo "========================================"
echo

echo "正在停止服务..."
if command -v python3 &> /dev/null; then
    python3 stop_server.py
elif command -v python &> /dev/null; then
    python stop_server.py
else
    echo "未找到Python环境"
fi

echo
echo "服务停止完成"
read -p "按回车键退出..."
'''

    # Linux环境测试脚本
    linux_test = '''#!/bin/bash
# Excel格式转换工具 - Linux环境测试

echo "========================================"
echo "     Excel格式转换工具 - 环境测试"
echo "========================================"
echo

echo "当前目录: $(pwd)"
echo "系统信息: $(uname -a)"
echo

echo "检查Python..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1)
    echo "✅ Python3已安装: $PYTHON_VERSION"
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_VERSION=$(python --version 2>&1)
    echo "✅ Python已安装: $PYTHON_VERSION"
    PYTHON_CMD="python"
else
    echo "❌ Python未安装"
    echo "💡 将自动安装Python环境"
    PYTHON_CMD=""
fi

echo

if [ -n "$PYTHON_CMD" ]; then
    echo "检查pip..."
    $PYTHON_CMD -m pip --version &> /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ pip已安装"
    else
        echo "❌ pip未安装"
    fi
fi

echo
echo "检查文件..."
if [ -f "app.py" ]; then
    echo "✅ app.py 存在"
else
    echo "❌ app.py 不存在"
fi

if [ -f "requirements.txt" ]; then
    echo "✅ requirements.txt 存在"
else
    echo "❌ requirements.txt 不存在"
fi

if [ -f "start_server.py" ]; then
    echo "✅ start_server.py 存在"
else
    echo "❌ start_server.py 不存在"
fi

echo

if [ -n "$PYTHON_CMD" ]; then
    echo "检查Flask..."
    $PYTHON_CMD -c "import flask; print('Flask版本:', flask.__version__)" 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Flask已安装"
    else
        echo "❌ Flask未安装"
        echo "💡 需要安装依赖包"
    fi
fi

echo
echo "========================================"
echo "测试完成"
echo "========================================"
echo
echo "如果Python未安装，启动脚本会自动安装"
echo "如果有其他问题，请根据提示解决"
echo
read -p "按回车键退出..."
'''

    # 写入Linux脚本文件
    scripts = [
        ("启动工具.sh", linux_start),
        ("install_python.sh", linux_python_installer),
        ("停止工具.sh", linux_stop),
        ("环境测试.sh", linux_test)
    ]

    for filename, content in scripts:
        script_path = os.path.join(release_dir, filename)
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(content)
        # 设置执行权限
        os.chmod(script_path, 0o755)
        print(f"  ✅ {filename}")

def create_python_installer(release_dir):
    """创建Python安装器"""
    print("📝 创建Python安装器...")

    # 创建refreshenv.cmd用于Windows刷新环境变量
    refreshenv_cmd = '''@echo off
REM 刷新环境变量的简单实现
REM 重新读取注册表中的环境变量

for /f "skip=2 tokens=3*" %%a in ('reg query HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session" Manager\\Environment" /v PATH') do set "SysPath=%%a %%b"
for /f "skip=2 tokens=3*" %%a in ('reg query HKCU\\Environment /v PATH 2^>nul') do set "UserPath=%%a %%b"

if defined UserPath (
    set "PATH=%SysPath%;%UserPath%"
) else (
    set "PATH=%SysPath%"
)

REM 刷新其他常用环境变量
for /f "skip=2 tokens=3*" %%a in ('reg query HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session" Manager\\Environment" /v PYTHONPATH 2^>nul') do set "PYTHONPATH=%%a %%b"
'''

    with open(os.path.join(release_dir, "refreshenv.cmd"), 'w', encoding='utf-8') as f:
        f.write(refreshenv_cmd)
    print("  ✅ refreshenv.cmd")

def create_documentation(release_dir):
    """创建说明文档"""
    print("📖 创建说明文档...")

    # 跨平台使用指南
    user_guide = '''# Excel格式转换工具 - 跨平台使用指南

## 🚀 快速开始

### Windows 11 用户

#### 自动安装模式（推荐）
1. 解压文件到任意目录
2. 双击 `启动工具.bat`
3. 首次运行会自动安装Python环境（需要管理员权限）
4. 等待启动完成
5. 在浏览器中访问: http://localhost:8080
6. 开始使用！

#### 手动安装模式
1. 如果自动安装失败，请手动安装Python
2. 访问 https://python.org 下载Python 3.11
3. 安装时务必勾选 "Add Python to PATH"
4. 重启电脑后运行 `启动工具.bat`

### Linux 用户

#### 自动安装模式（推荐）
1. 解压文件到任意目录
2. 打开终端，进入解压目录
3. 运行: `./启动工具.sh`
4. 首次运行会自动安装Python环境（需要sudo权限）
5. 等待启动完成
6. 在浏览器中访问: http://localhost:8080
7. 开始使用！

#### 手动安装模式
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip

# CentOS/RHEL
sudo yum install python3 python3-pip

# Fedora
sudo dnf install python3 python3-pip

# Arch Linux
sudo pacman -S python python-pip
```

## 🔧 故障排除

### Windows 11 常见问题

#### 1. "'.' 不是内部或外部命令" 错误
**已解决**: 使用UTF-8编码和标准批处理语法

#### 2. 中文乱码问题
**已解决**: 脚本开头使用 `chcp 65001` 设置UTF-8编码

#### 3. Python自动安装失败
**解决方案**:
- 以管理员身份运行 `启动工具.bat`
- 检查网络连接
- 手动下载安装Python: https://python.org

#### 4. 权限问题
**解决方案**:
- 右键点击脚本，选择"以管理员身份运行"
- 或在管理员命令提示符中运行

#### 5. 防火墙阻止
**解决方案**:
- 允许Python程序通过Windows防火墙
- 或临时关闭防火墙测试

### Linux 常见问题

#### 1. 权限不足
**解决方案**:
```bash
chmod +x *.sh
sudo ./启动工具.sh
```

#### 2. Python安装失败
**解决方案**:
- 检查网络连接
- 更新包管理器: `sudo apt update` 或 `sudo yum update`
- 手动安装Python

#### 3. 端口被占用
**解决方案**:
```bash
# 查看占用8080端口的进程
sudo netstat -tlnp | grep :8080
# 杀死进程
sudo kill -9 <进程ID>
```

## 📋 系统要求

### Windows
- Windows 7 或更高版本（推荐Windows 10/11）
- 100MB 可用磁盘空间
- 管理员权限（用于自动安装Python）
- 现代Web浏览器

### Linux
- 主流Linux发行版（Ubuntu、CentOS、Fedora、Debian等）
- 100MB 可用磁盘空间
- sudo权限（用于自动安装Python）
- 现代Web浏览器

## 💡 使用技巧

### 文件上传
- 支持拖拽上传
- 支持 .xlsx 和 .xls 格式
- 最大文件大小: 16MB

### 转换功能
- 自动识别多工作表
- 智能字段映射
- 完整数据保留
- 详细转换报告

### 性能优化
- 建议关闭不必要的程序释放内存
- 大文件转换时请耐心等待
- 转换完成后及时下载结果文件

## 📞 技术支持

### 环境测试
- Windows: 双击 `环境测试.bat`
- Linux: 运行 `./环境测试.sh`

### 日志查看
- 启动脚本会显示详细的执行日志
- 遇到问题时请查看错误信息

### 联系支持
如果遇到无法解决的问题：
1. 运行环境测试脚本
2. 记录错误信息
3. 联系技术支持团队

---
**Excel格式转换工具开发团队**
'''

    # 版本说明
    version_info = '''# 版本说明 v2.1 - 跨平台版

## 🔧 新增功能

### Windows 11 完全兼容
- ✅ 解决 "'.' 不是内部或外部命令" 错误
- ✅ 修复中文乱码问题（使用UTF-8编码）
- ✅ 自动Python环境安装
- ✅ 智能环境变量配置
- ✅ 完善的错误处理

### Linux 全面支持
- ✅ 支持主流Linux发行版
- ✅ 自动包管理器检测
- ✅ 智能Python安装
- ✅ 完整的Shell脚本支持

### 自动化安装
- ✅ Windows: 自动下载安装Python
- ✅ Linux: 自动使用包管理器安装
- ✅ 智能架构检测（x86/x64/ARM）
- ✅ 环境变量自动配置

### 用户体验改进
- ✅ 详细的安装进度显示
- ✅ 友好的错误提示
- ✅ 完整的故障排除指南
- ✅ 环境测试工具

## 🔧 技术改进

### 编码问题解决
- 使用UTF-8 BOM编码保存Windows批处理文件
- 脚本开头设置 `chcp 65001` 确保UTF-8显示
- 标准化所有中文字符编码

### 批处理语法优化
- 避免使用可能导致歧义的 `echo.` 语法
- 使用 `setlocal enabledelayedexpansion` 处理变量
- 标准化错误检查逻辑

### 跨平台兼容性
- Windows: 支持Windows 7-11所有版本
- Linux: 支持Ubuntu、CentOS、Fedora、Debian、Arch等
- 智能检测系统环境和包管理器

### 安全性增强
- 验证下载文件完整性
- 安全的静默安装参数
- 权限检查和提示

## 📁 文件说明

### Windows 文件
- `启动工具.bat` - 主启动脚本
- `停止工具.bat` - 停止服务脚本
- `环境测试.bat` - 环境检查工具
- `install_python.bat` - Python自动安装器
- `refreshenv.cmd` - 环境变量刷新工具

### Linux 文件
- `启动工具.sh` - 主启动脚本
- `停止工具.sh` - 停止服务脚本
- `环境测试.sh` - 环境检查工具
- `install_python.sh` - Python自动安装器

### 核心文件
- `app.py` - Flask Web应用
- `start_server.py` - 服务器启动脚本
- `stop_server.py` - 服务器停止脚本
- `requirements.txt` - Python依赖列表

---
发布日期: ''' + datetime.now().strftime("%Y-%m-%d") + '''
版本: v2.1 跨平台版
'''

    # 写入文档
    with open(os.path.join(release_dir, "使用指南.md"), 'w', encoding='utf-8') as f:
        f.write(user_guide)
    print("  ✅ 使用指南.md")

    with open(os.path.join(release_dir, "版本说明.md"), 'w', encoding='utf-8') as f:
        f.write(version_info)
    print("  ✅ 版本说明.md")

def create_zip_package(release_dir, zip_path):
    """创建ZIP包"""
    print("📦 创建ZIP包...")

    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(release_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_path = os.path.relpath(file_path, os.path.dirname(release_dir))
                zipf.write(file_path, arc_path)

    print("  ✅ ZIP包创建完成")

def main():
    """主函数"""
    if not os.path.exists("app.py"):
        print("❌ 未找到app.py文件，请在项目根目录运行此脚本")
        return

    zip_path = create_cross_platform_release()

    print(f"\n🎉 跨平台发布包创建完成!")
    print(f"\n📋 解决的问题:")
    print(f"  • Windows 11 完全兼容")
    print(f"  • 中文乱码问题修复")
    print(f"  • '.' 命令错误解决")
    print(f"  • 自动Python环境安装")
    print(f"  • Linux全面支持")
    print(f"  • 跨平台统一体验")

    print(f"\n💡 使用方法:")
    print(f"  Windows: 双击 '启动工具.bat'")
    print(f"  Linux: 运行 './启动工具.sh'")
    print(f"  访问: http://localhost:8080")

    print(f"\n📤 发布包文件: {zip_path}")

if __name__ == '__main__':
    main()
