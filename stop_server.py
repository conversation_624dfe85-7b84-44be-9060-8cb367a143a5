#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel格式转换工具 - 服务器停止脚本
"""

import os
import sys
import signal
import psutil
import time

def print_banner():
    """打印停止横幅"""
    print("=" * 60)
    print("           Excel格式转换工具 - 服务器停止")
    print("=" * 60)

def find_server_processes():
    """查找运行中的服务器进程"""
    processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('app.py' in cmd or 'start_server.py' in cmd for cmd in cmdline):
                # 检查是否是Python进程且包含我们的脚本
                if 'python' in proc.info['name'].lower():
                    processes.append(proc)
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    return processes

def stop_server():
    """停止服务器"""
    print_banner()
    
    # 查找服务器进程
    processes = find_server_processes()
    
    if not processes:
        print("🔍 未找到运行中的Excel格式转换工具服务器")
        print("✅ 服务器可能已经停止")
        return
    
    print(f"🔍 找到 {len(processes)} 个服务器进程")
    
    # 停止进程
    stopped_count = 0
    for proc in processes:
        try:
            print(f"🛑 正在停止进程 PID: {proc.pid}")
            
            # 首先尝试优雅停止
            proc.terminate()
            
            # 等待进程结束
            try:
                proc.wait(timeout=5)
                print(f"✅ 进程 {proc.pid} 已优雅停止")
                stopped_count += 1
            except psutil.TimeoutExpired:
                # 如果优雅停止失败，强制杀死
                print(f"⚠️  进程 {proc.pid} 未响应，强制停止...")
                proc.kill()
                proc.wait(timeout=3)
                print(f"✅ 进程 {proc.pid} 已强制停止")
                stopped_count += 1
                
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f"❌ 无法停止进程 {proc.pid}: {e}")
        except Exception as e:
            print(f"❌ 停止进程时出错: {e}")
    
    if stopped_count > 0:
        print(f"\n🎉 成功停止 {stopped_count} 个服务器进程")
        print("✅ Excel格式转换工具服务器已停止")
    else:
        print("\n❌ 未能停止任何进程")
    
    # 额外检查端口占用
    check_port_usage()

def check_port_usage():
    """检查端口8080是否还在使用"""
    try:
        for conn in psutil.net_connections():
            if conn.laddr.port == 8080 and conn.status == 'LISTEN':
                print(f"⚠️  端口8080仍被进程 {conn.pid} 占用")
                try:
                    proc = psutil.Process(conn.pid)
                    print(f"   进程信息: {proc.name()} (PID: {proc.pid})")
                    
                    # 询问是否强制停止
                    response = input("是否强制停止该进程? (y/N): ").strip().lower()
                    if response in ['y', 'yes']:
                        proc.kill()
                        print(f"✅ 已强制停止进程 {proc.pid}")
                except Exception as e:
                    print(f"❌ 处理端口占用进程时出错: {e}")
                return
        
        print("✅ 端口8080已释放")
        
    except Exception as e:
        print(f"⚠️  检查端口占用时出错: {e}")

def main():
    """主函数"""
    try:
        stop_server()
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 停止服务器时发生错误: {e}")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    input("按回车键退出...")

if __name__ == '__main__':
    main()
