@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     Python Auto Installer
echo ========================================
echo.

REM Check system architecture
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set ARCH=amd64
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    set ARCH=win32
) else (
    set ARCH=amd64
)

echo Detected system architecture: !ARCH!
echo.

REM Set Python download URL
set PYTHON_VERSION=3.11.5
if "!ARCH!"=="amd64" (
    set PYTHON_URL=https://www.python.org/ftp/python/!PYTHON_VERSION!/python-!PYTHON_VERSION!-amd64.exe
    set PYTHON_FILE=python-!PYTHON_VERSION!-amd64.exe
) else (
    set PYTHON_URL=https://www.python.org/ftp/python/!PYTHON_VERSION!/python-!PYTHON_VERSION!.exe
    set PYTHON_FILE=python-!PYTHON_VERSION!.exe
)

echo Preparing to download Python !PYTHON_VERSION!...
echo Download URL: !PYTHON_URL!
echo.

REM Check for download tools
where curl >nul 2>&1
if !errorlevel! equ 0 (
    echo Using curl to download Python...
    curl -L -o "!PYTHON_FILE!" "!PYTHON_URL!"
) else (
    where powershell >nul 2>&1
    if !errorlevel! equ 0 (
        echo Using PowerShell to download Python...
        powershell -Command "Invoke-WebRequest -Uri '!PYTHON_URL!' -OutFile '!PYTHON_FILE!'"
    ) else (
        echo Cannot find download tool
        echo Please manually download Python: !PYTHON_URL!
        echo After download, run installer and check "Add Python to PATH"
        pause
        exit /b 1
    )
)

if not exist "!PYTHON_FILE!" (
    echo Python download failed
    echo Please manually download and install Python: https://python.org
    pause
    exit /b 1
)

echo Python download completed, starting installation...
echo.

REM Silent install Python
"!PYTHON_FILE!" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0

REM Wait for installation to complete
timeout /t 30 /nobreak >nul

REM Refresh environment variables
call refreshenv.cmd >nul 2>&1

REM Verify installation
python --version >nul 2>&1
if !errorlevel! equ 0 (
    echo Python installation successful!
    del "!PYTHON_FILE!" >nul 2>&1
) else (
    echo Python installation may have failed, please restart computer and try again
    echo Or manually install Python: https://python.org
    pause
    exit /b 1
)

echo.
echo Python environment configuration completed
