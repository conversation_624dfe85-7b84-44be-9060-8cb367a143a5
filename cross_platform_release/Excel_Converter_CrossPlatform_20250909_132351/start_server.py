'''
Author: <PERSON><PERSON><PERSON> <EMAIL>
Date: 2025-09-08 11:25:09
LastEditors: songjian <EMAIL>
LastEditTime: 2025-09-08 11:27:42
FilePath: /FormatConversion/start_server.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel格式转换工具启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from threading import Timer

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import pandas
        import openpyxl
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError:
        print("❌ 依赖安装失败")
        return False

def open_browser():
    """延迟打开浏览器"""
    time.sleep(2)
    webbrowser.open('http://localhost:8080')

def main():
    """主函数"""
    print("=" * 60)
    print("           Excel格式转换工具")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n正在安装缺少的依赖...")
        if not install_dependencies():
            print("请手动运行: pip install -r requirements.txt")
            return
    
    # 创建必要的文件夹
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('outputs', exist_ok=True)
    
    print("\n🚀 启动Web服务器...")
    print("📱 访问地址: http://localhost:8080")
    print("🛑 按 Ctrl+C 停止服务器")
    print("-" * 60)
    
    # 延迟打开浏览器
    Timer(2.0, open_browser).start()
    
    # 启动Flask应用
    try:
        from app import app
        app.run(debug=False, host='0.0.0.0', port=8080)
    except KeyboardInterrupt:
        print("\n\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
