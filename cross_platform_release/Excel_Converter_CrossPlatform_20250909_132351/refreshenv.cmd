@echo off
REM 刷新环境变量的简单实现
REM 重新读取注册表中的环境变量

for /f "skip=2 tokens=3*" %%a in ('reg query HKLM\SYSTEM\CurrentControlSet\Control\Session" Manager\Environment" /v PATH') do set "SysPath=%%a %%b"
for /f "skip=2 tokens=3*" %%a in ('reg query HKCU\Environment /v PATH 2^>nul') do set "UserPath=%%a %%b"

if defined UserPath (
    set "PATH=%SysPath%;%UserPath%"
) else (
    set "PATH=%SysPath%"
)

REM 刷新其他常用环境变量
for /f "skip=2 tokens=3*" %%a in ('reg query HKLM\SYSTEM\CurrentControlSet\Control\Session" Manager\Environment" /v PYTHONPATH 2^>nul') do set "PYTHONPATH=%%a %%b"
