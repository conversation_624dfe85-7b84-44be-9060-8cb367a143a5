#!/bin/bash
# Python自动安装脚本 - Linux

echo "========================================"
echo "     Python自动安装程序 - Linux"
echo "========================================"
echo

# 检测Linux发行版
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
else
    echo "无法检测Linux发行版"
    OS="Unknown"
fi

echo "检测到系统: $OS"
echo

# 根据发行版安装Python
case $OS in
    *"Ubuntu"*|*"Debian"*)
        echo "使用apt安装Python..."
        sudo apt update
        sudo apt install -y python3 python3-pip python3-venv
        ;;
    *"CentOS"*|*"Red Hat"*|*"Fedora"*)
        echo "使用yum/dnf安装Python..."
        if command -v dnf &> /dev/null; then
            sudo dnf install -y python3 python3-pip
        else
            sudo yum install -y python3 python3-pip
        fi
        ;;
    *"Arch"*)
        echo "使用pacman安装Python..."
        sudo pacman -S --noconfirm python python-pip
        ;;
    *"openSUSE"*)
        echo "使用zypper安装Python..."
        sudo zypper install -y python3 python3-pip
        ;;
    *)
        echo "不支持的Linux发行版: $OS"
        echo "请手动安装Python 3.7或更高版本"
        echo "安装命令示例:"
        echo "  Ubuntu/Debian: sudo apt install python3 python3-pip"
        echo "  CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "  Fedora: sudo dnf install python3 python3-pip"
        echo "  Arch: sudo pacman -S python python-pip"
        exit 1
        ;;
esac

# 验证安装
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1)
    echo "Python安装成功: $PYTHON_VERSION"
else
    echo "Python安装失败"
    exit 1
fi

echo
echo "Python环境配置完成"
