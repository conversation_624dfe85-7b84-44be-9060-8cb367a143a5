@echo off
setlocal enabledelayedexpansion
title Environment Test

echo ========================================
echo     Excel Converter - Environment Test
echo ========================================
echo.

echo Current directory: %CD%
echo System version: 
ver
echo.

echo Checking Python...
python --version 2>&1
if !errorlevel! neq 0 (
    echo Python not installed
    echo Will auto-install Python environment
) else (
    echo Python installed
)

echo.
echo Checking pip...
pip --version 2>&1
if !errorlevel! neq 0 (
    echo pip not installed
) else (
    echo pip installed
)

echo.
echo Checking files...
if exist "app.py" (
    echo app.py EXISTS
) else (
    echo app.py NOT FOUND
)

if exist "requirements.txt" (
    echo requirements.txt EXISTS
) else (
    echo requirements.txt NOT FOUND
)

if exist "start_server.py" (
    echo start_server.py EXISTS
) else (
    echo start_server.py NOT FOUND
)

echo.
echo Checking Flask...
python -c "import flask; print('Flask version:', flask.__version__)" 2>&1
if !errorlevel! neq 0 (
    echo Flask not installed
    echo Need to install dependencies
) else (
    echo Flask installed
)

echo.
echo ========================================
echo Test completed
echo ========================================
echo.
echo If Python not installed, startup script will auto-install
echo If other issues, please solve according to prompts
echo.
pause
