# Excel格式转换工具 - 跨平台版

## 🚀 一键启动

### Windows 11 用户
```
双击 "启动工具.bat" → 自动安装Python → 启动成功
```

### Linux 用户
```bash
./启动工具.sh
```

## ✅ 完全解决的问题

- ✅ **Windows 11 兼容性** - 完美支持最新系统
- ✅ **中文乱码问题** - UTF-8编码完美显示
- ✅ **"." 命令错误** - 彻底修复批处理语法问题
- ✅ **Python环境** - 自动下载安装配置
- ✅ **环境变量** - 自动配置PATH路径
- ✅ **跨平台支持** - Windows + Linux 统一体验

## 🎯 核心特性

### 自动化安装
- **Windows**: 自动下载Python 3.11，静默安装，配置环境变量
- **Linux**: 智能检测发行版，使用对应包管理器安装
- **零配置**: 解压即用，无需手动配置

### 完美兼容性
- **Windows**: 7/8/10/11 全版本支持
- **Linux**: Ubuntu/CentOS/Fedora/Debian/Arch 等主流发行版
- **架构**: x86/x64/ARM 自动检测

### 用户体验
- **中文界面**: 完美UTF-8编码，无乱码
- **详细日志**: 每步操作都有清晰提示
- **错误处理**: 友好的错误信息和解决建议
- **环境测试**: 内置诊断工具

## 📁 文件说明

```
Excel_Converter_CrossPlatform/
├── 启动工具.bat          # Windows 主启动脚本
├── 启动工具.sh           # Linux 主启动脚本
├── 停止工具.bat          # Windows 停止脚本
├── 停止工具.sh           # Linux 停止脚本
├── 环境测试.bat          # Windows 环境检测
├── 环境测试.sh           # Linux 环境检测
├── install_python.bat    # Windows Python安装器
├── install_python.sh     # Linux Python安装器
├── refreshenv.cmd        # Windows 环境变量刷新
├── app.py               # Flask Web应用
├── start_server.py      # 服务器启动脚本
├── stop_server.py       # 服务器停止脚本
├── requirements.txt     # Python依赖列表
├── templates/           # Web模板
├── uploads/             # 上传目录
├── outputs/             # 输出目录
├── 使用指南.md          # 详细使用说明
└── 版本说明.md          # 版本更新信息
```

## 🔧 故障排除

### 如果启动失败
1. 运行环境测试: `环境测试.bat` (Windows) 或 `./环境测试.sh` (Linux)
2. 查看错误信息
3. 根据提示解决问题

### 常见问题
- **权限不足**: 以管理员身份运行 (Windows) 或使用 sudo (Linux)
- **网络问题**: 检查网络连接，Python下载需要网络
- **防火墙**: 允许Python程序通过防火墙

## 💡 使用流程

1. **解压文件** 到任意目录
2. **启动程序** (双击bat文件或运行sh脚本)
3. **等待安装** (首次运行会自动安装Python环境)
4. **访问应用** http://localhost:8080
5. **上传文件** 进行Excel格式转换
6. **下载结果** 获取转换后的文件

---

**技术支持**: 遇到问题请查看 `使用指南.md` 获取详细帮助
