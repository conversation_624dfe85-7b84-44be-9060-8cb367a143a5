<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel格式转换工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .header {
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }
        
        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 15px;
            padding: 60px 20px;
            margin: 30px 0;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #2980b9;
            background: #e3f2fd;
        }
        
        .upload-area.dragover {
            border-color: #27ae60;
            background: #e8f5e8;
        }
        
        .upload-icon {
            font-size: 4em;
            color: #3498db;
            margin-bottom: 20px;
        }
        
        .upload-text {
            font-size: 1.2em;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .upload-hint {
            color: #7f8c8d;
            font-size: 0.9em;
        }
        
        #fileInput {
            display: none;
        }
        
        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(52, 152, 219, 0.3);
        }
        
        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .progress {
            display: none;
            margin: 20px 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            width: 0%;
            transition: width 0.3s ease;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .result {
            display: none;
            margin-top: 30px;
            padding: 20px;
            border-radius: 10px;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #27ae60;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 5px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            text-decoration: none;
            padding: 15px 30px;
            border-radius: 25px;
            display: inline-block;
            margin-top: 20px;
            transition: all 0.3s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(39, 174, 96, 0.3);
        }
        
        .features {
            margin-top: 40px;
            text-align: left;
        }
        
        .features h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
        }
        
        .features li {
            padding: 8px 0;
            color: #7f8c8d;
        }
        
        .features li:before {
            content: "✅ ";
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Excel格式转换工具</h1>
            <p>将螺栓清单转换为标准格式，支持竖向布局和备注提取</p>
        </div>
        
        <div class="upload-area" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📁</div>
            <div class="upload-text">点击选择文件或拖拽文件到此处</div>
            <div class="upload-hint">支持 .xlsx 和 .xls 格式，最大16MB</div>
        </div>
        
        <input type="file" id="fileInput" accept=".xlsx,.xls">
        
        <button class="btn" id="uploadBtn" disabled>开始转换</button>
        
        <div class="progress" id="progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p>正在转换中，请稍候...</p>
        </div>
        
        <div class="result" id="result">
            <div id="resultContent"></div>
        </div>
        
        <div class="features">
            <h3>🎯 功能特点</h3>
            <ul>
                <li>横向段别数据转竖向格式</li>
                <li>自动提取项目信息（塔型、呼高等）</li>
                <li>备注信息智能提取</li>
                <li>数量自动计算（含5%额外）</li>
                <li>生成详细转换报告</li>
                <li>一键下载结果文件包</li>
            </ul>
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const uploadArea = document.querySelector('.upload-area');
        const progress = document.getElementById('progress');
        const progressFill = document.getElementById('progressFill');
        const result = document.getElementById('result');
        const resultContent = document.getElementById('resultContent');
        
        // 文件选择处理
        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                const file = e.target.files[0];
                uploadBtn.disabled = false;
                uploadArea.innerHTML = `
                    <div class="upload-icon">📄</div>
                    <div class="upload-text">已选择: ${file.name}</div>
                    <div class="upload-hint">文件大小: ${(file.size / 1024 / 1024).toFixed(2)} MB</div>
                `;
            }
        });
        
        // 拖拽处理
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                fileInput.dispatchEvent(new Event('change'));
            }
        });
        
        // 上传处理
        uploadBtn.addEventListener('click', function() {
            const file = fileInput.files[0];
            if (!file) return;
            
            const formData = new FormData();
            formData.append('file', file);
            
            // 显示进度
            progress.style.display = 'block';
            result.style.display = 'none';
            uploadBtn.disabled = true;
            
            // 模拟进度
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += Math.random() * 30;
                if (progressValue > 90) progressValue = 90;
                progressFill.style.width = progressValue + '%';
            }, 500);
            
            // 发送请求
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                clearInterval(progressInterval);
                progressFill.style.width = '100%';
                
                setTimeout(() => {
                    progress.style.display = 'none';
                    result.style.display = 'block';
                    uploadBtn.disabled = false;
                    
                    if (data.success) {
                        result.className = 'result success';
                        resultContent.innerHTML = `
                            <h3>🎉 转换成功！</h3>
                            <div class="stats">
                                <div class="stat-item">
                                    <div class="stat-number">${data.result.records}</div>
                                    <div class="stat-label">转换记录</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${data.result.processed_sheets || 1}</div>
                                    <div class="stat-label">处理工作表</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${data.result.material_types}</div>
                                    <div class="stat-label">物料种类</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${data.result.spec_types}</div>
                                    <div class="stat-label">规格种类</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number">${data.result.total_quantity}</div>
                                    <div class="stat-label">总数量</div>
                                </div>
                            </div>
                            <p><strong>项目信息:</strong> ${data.result.project_info.tower_type} | 呼高${data.result.project_info.height}</p>
                            <a href="${data.download_url}" class="download-btn">📥 下载转换结果包</a>
                        `;
                    } else {
                        result.className = 'result error';
                        resultContent.innerHTML = `
                            <h3>❌ 转换失败</h3>
                            <p>${data.error}</p>
                        `;
                    }
                }, 1000);
            })
            .catch(error => {
                clearInterval(progressInterval);
                progress.style.display = 'none';
                result.style.display = 'block';
                result.className = 'result error';
                resultContent.innerHTML = `
                    <h3>❌ 网络错误</h3>
                    <p>${error.message}</p>
                `;
                uploadBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
