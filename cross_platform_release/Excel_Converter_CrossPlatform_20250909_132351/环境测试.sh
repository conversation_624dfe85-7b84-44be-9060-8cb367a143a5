#!/bin/bash
# Excel格式转换工具 - Linux环境测试

echo "========================================"
echo "     Excel格式转换工具 - 环境测试"
echo "========================================"
echo

echo "当前目录: $(pwd)"
echo "系统信息: $(uname -a)"
echo

echo "检查Python..."
if command -v python3 &> /dev/null; then
    PYTHON_VERSION=$(python3 --version 2>&1)
    echo "✅ Python3已安装: $PYTHON_VERSION"
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_VERSION=$(python --version 2>&1)
    echo "✅ Python已安装: $PYTHON_VERSION"
    PYTHON_CMD="python"
else
    echo "❌ Python未安装"
    echo "💡 将自动安装Python环境"
    PYTHON_CMD=""
fi

echo

if [ -n "$PYTHON_CMD" ]; then
    echo "检查pip..."
    $PYTHON_CMD -m pip --version &> /dev/null
    if [ $? -eq 0 ]; then
        echo "✅ pip已安装"
    else
        echo "❌ pip未安装"
    fi
fi

echo
echo "检查文件..."
if [ -f "app.py" ]; then
    echo "✅ app.py 存在"
else
    echo "❌ app.py 不存在"
fi

if [ -f "requirements.txt" ]; then
    echo "✅ requirements.txt 存在"
else
    echo "❌ requirements.txt 不存在"
fi

if [ -f "start_server.py" ]; then
    echo "✅ start_server.py 存在"
else
    echo "❌ start_server.py 不存在"
fi

echo

if [ -n "$PYTHON_CMD" ]; then
    echo "检查Flask..."
    $PYTHON_CMD -c "import flask; print('Flask版本:', flask.__version__)" 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Flask已安装"
    else
        echo "❌ Flask未安装"
        echo "💡 需要安装依赖包"
    fi
fi

echo
echo "========================================"
echo "测试完成"
echo "========================================"
echo
echo "如果Python未安装，启动脚本会自动安装"
echo "如果有其他问题，请根据提示解决"
echo
read -p "按回车键退出..."
