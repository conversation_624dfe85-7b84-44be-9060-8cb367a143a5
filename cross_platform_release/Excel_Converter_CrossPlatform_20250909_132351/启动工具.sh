#!/bin/bash
# Excel格式转换工具 - Linux启动脚本

echo "========================================"
echo "     Excel格式转换工具 v2.1"
echo "========================================"
echo
echo "正在启动..."
echo

# 检查Python环境
echo "检查Python环境..."
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
elif command -v python &> /dev/null; then
    PYTHON_CMD="python"
else
    echo "Python未安装，正在自动安装..."
    ./install_python.sh
    if [ $? -ne 0 ]; then
        echo "Python安装失败，请手动安装"
        read -p "按回车键退出..."
        exit 1
    fi
    PYTHON_CMD="python3"
fi

# 显示Python版本
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1)
echo "Python环境正常 ($PYTHON_VERSION)"
echo

# 检查必需文件
echo "检查程序文件..."
if [ ! -f "app.py" ]; then
    echo "错误: 未找到app.py文件"
    echo "请确保在正确目录运行此脚本"
    read -p "按回车键退出..."
    exit 1
fi

if [ ! -f "requirements.txt" ]; then
    echo "错误: 未找到requirements.txt文件"
    echo "请确保在正确目录运行此脚本"
    read -p "按回车键退出..."
    exit 1
fi

echo "程序文件检查通过"
echo

# 检查并安装依赖
echo "检查依赖包..."
$PYTHON_CMD -c "import flask" &> /dev/null
if [ $? -ne 0 ]; then
    echo "安装依赖包..."
    echo "这可能需要几分钟，请耐心等待..."
    echo

    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "依赖安装失败，尝试使用镜像源..."
        $PYTHON_CMD -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if [ $? -ne 0 ]; then
            echo "依赖安装失败，请检查网络连接"
            read -p "按回车键退出..."
            exit 1
        fi
    fi
    echo "依赖包安装完成"
else
    echo "依赖包已安装"
fi

echo
echo "========================================"
echo "启动Excel格式转换工具..."
echo
echo "请在浏览器中访问: http://localhost:8080"
echo "按 Ctrl+C 停止服务器"
echo "========================================"
echo

# 启动服务器
$PYTHON_CMD start_server.py

echo
echo "服务器已停止"
read -p "按回车键退出..."
