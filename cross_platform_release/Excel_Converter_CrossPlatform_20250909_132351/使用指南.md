# Excel格式转换工具 - 跨平台使用指南

## 🚀 快速开始

### Windows 11 用户

#### 自动安装模式（推荐）
1. 解压文件到任意目录
2. 双击 `启动工具.bat`
3. 首次运行会自动安装Python环境（需要管理员权限）
4. 等待启动完成
5. 在浏览器中访问: http://localhost:8080
6. 开始使用！

#### 手动安装模式
1. 如果自动安装失败，请手动安装Python
2. 访问 https://python.org 下载Python 3.11
3. 安装时务必勾选 "Add Python to PATH"
4. 重启电脑后运行 `启动工具.bat`

### Linux 用户

#### 自动安装模式（推荐）
1. 解压文件到任意目录
2. 打开终端，进入解压目录
3. 运行: `./启动工具.sh`
4. 首次运行会自动安装Python环境（需要sudo权限）
5. 等待启动完成
6. 在浏览器中访问: http://localhost:8080
7. 开始使用！

#### 手动安装模式
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-pip

# CentOS/RHEL
sudo yum install python3 python3-pip

# Fedora
sudo dnf install python3 python3-pip

# Arch Linux
sudo pacman -S python python-pip
```

## 🔧 故障排除

### Windows 11 常见问题

#### 1. "'.' 不是内部或外部命令" 错误
**已解决**: 使用UTF-8编码和标准批处理语法

#### 2. 中文乱码问题
**已解决**: 脚本开头使用 `chcp 65001` 设置UTF-8编码

#### 3. Python自动安装失败
**解决方案**:
- 以管理员身份运行 `启动工具.bat`
- 检查网络连接
- 手动下载安装Python: https://python.org

#### 4. 权限问题
**解决方案**:
- 右键点击脚本，选择"以管理员身份运行"
- 或在管理员命令提示符中运行

#### 5. 防火墙阻止
**解决方案**:
- 允许Python程序通过Windows防火墙
- 或临时关闭防火墙测试

### Linux 常见问题

#### 1. 权限不足
**解决方案**:
```bash
chmod +x *.sh
sudo ./启动工具.sh
```

#### 2. Python安装失败
**解决方案**:
- 检查网络连接
- 更新包管理器: `sudo apt update` 或 `sudo yum update`
- 手动安装Python

#### 3. 端口被占用
**解决方案**:
```bash
# 查看占用8080端口的进程
sudo netstat -tlnp | grep :8080
# 杀死进程
sudo kill -9 <进程ID>
```

## 📋 系统要求

### Windows
- Windows 7 或更高版本（推荐Windows 10/11）
- 100MB 可用磁盘空间
- 管理员权限（用于自动安装Python）
- 现代Web浏览器

### Linux
- 主流Linux发行版（Ubuntu、CentOS、Fedora、Debian等）
- 100MB 可用磁盘空间
- sudo权限（用于自动安装Python）
- 现代Web浏览器

## 💡 使用技巧

### 文件上传
- 支持拖拽上传
- 支持 .xlsx 和 .xls 格式
- 最大文件大小: 16MB

### 转换功能
- 自动识别多工作表
- 智能字段映射
- 完整数据保留
- 详细转换报告

### 性能优化
- 建议关闭不必要的程序释放内存
- 大文件转换时请耐心等待
- 转换完成后及时下载结果文件

## 📞 技术支持

### 环境测试
- Windows: 双击 `环境测试.bat`
- Linux: 运行 `./环境测试.sh`

### 日志查看
- 启动脚本会显示详细的执行日志
- 遇到问题时请查看错误信息

### 联系支持
如果遇到无法解决的问题：
1. 运行环境测试脚本
2. 记录错误信息
3. 联系技术支持团队

---
**Excel格式转换工具开发团队**
