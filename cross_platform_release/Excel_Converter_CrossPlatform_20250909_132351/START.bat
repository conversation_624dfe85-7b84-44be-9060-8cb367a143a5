@echo off
setlocal enabledelayedexpansion
title Excel Format Converter

echo ========================================
echo     Excel Format Converter v2.1
echo ========================================
echo.
echo Starting...
echo.

REM Check Python environment
echo Checking Python environment...
python --version >nul 2>&1
if !errorlevel! neq 0 (
    echo Python not installed, installing automatically...
    call install_python_en.bat
    if !errorlevel! neq 0 (
        echo Python installation failed, please install manually
        pause
        exit /b 1
    )
)

REM Display Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Python environment OK (Version: !PYTHON_VERSION!)
echo.

REM Check required files
echo Checking program files...
if not exist "app.py" (
    echo ERROR: app.py not found
    echo Please run this script in the correct directory
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ERROR: requirements.txt not found
    echo Please run this script in the correct directory
    pause
    exit /b 1
)

echo Program files check passed
echo.

REM Check and install dependencies
echo Checking dependencies...
python -c "import flask" >nul 2>&1
if !errorlevel! neq 0 (
    echo Installing dependencies...
    echo This may take a few minutes, please wait...
    echo.
    
    pip install -r requirements.txt
    if !errorlevel! neq 0 (
        echo Installation failed, trying mirror...
        pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
        if !errorlevel! neq 0 (
            echo Installation failed, please check network connection
            pause
            exit /b 1
        )
    )
    echo Dependencies installed successfully
) else (
    echo Dependencies already installed
)

echo.
echo ========================================
echo Starting Excel Format Converter...
echo.
echo Please visit: http://localhost:8080
echo Press Ctrl+C to stop server
echo ========================================
echo.

REM Start server
python start_server.py

echo.
echo Server stopped
pause
