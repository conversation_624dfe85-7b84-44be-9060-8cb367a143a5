# Excel格式转换工具 V2.1 发布说明

## 🎉 版本信息
- **版本**: V2.1
- **发布日期**: 2025年9月9日
- **包文件**: `excel_converter_web_20250909_084258.zip`

## 🆕 新功能特性

### 🔄 多工作表支持
- **自动发现**: 不再固定读取特定工作表名称，自动处理Excel文件中的所有工作表
- **智能跳过**: 自动跳过空的或无效的工作表
- **统一输出**: 所有工作表数据合并到一个结果文件中
- **来源标记**: 每条记录标明来源工作表

### 🎯 动态字段识别
- **智能定位**: 自动识别不同工作表中字段的位置变化
- **6个关键字段**: 全部支持动态位置获取
  - 单基 QTY/TOWER
  - 单基总数 TOTAL AMOUNT/TOWER
  - 加5% EXTRA 5%
  - 总数 TOTAL QUANTITY
  - 备注 REMARKS
  - 订单号 ORDER NO. (新增)

### 🆕 V2.1 新增功能
- **订单号字段**: 动态提取"订单号/ORDER NO.：GH25019A"字段值
- **智能解析**: 自动提取冒号后的订单号值
- **完整集成**: 订单号列完全集成到转换结果中

### 📊 增强的数据结构
- **新增列**: 增加"单基总数"列和"订单号"列
- **正确映射**:
  - "单基总数 TOTAL AMOUNT/TOWER" → "单基总数"列
  - "总数 TOTAL QUANTITY" → "总数"列
  - "订单号/ORDER NO.：GH25019A" → "订单号"列
- **完整字段**: 18个字段完整覆盖

### 📁 文件命名优化
- **智能命名**: 输出文件名改为"原文件名_转换后.xlsx"
- **示例**: 
  - 上传"2.xlsx" → 生成"2_转换后.xlsx"
  - 上传"5.xlsx" → 生成"5_转换后.xlsx"

### 🧹 自动缓存清理
- **下载后清理**: 文件下载完成后自动清空服务器缓存
- **延迟清理**: 使用后台线程确保下载完成后再清理
- **完整清理**: 清理uploads和outputs文件夹中的相关文件

## 🎨 用户界面
- **响应式设计**: 适配各种屏幕尺寸
- **拖拽上传**: 直观的文件上传体验
- **实时反馈**: 显示处理的工作表数量
- **详细统计**: 完整的转换结果展示

## 📈 性能改进
- **容错处理**: 单个工作表出错不影响其他工作表
- **数据验证**: 自动检查工作表数据有效性
- **智能默认**: 找不到字段时使用合理默认值
- **100%准确**: 所有字段值与源文件完全匹配

## 🔧 技术特点
- **Flask Web框架**: 稳定的Web服务
- **pandas数据处理**: 高效的Excel文件操作
- **动态字段查找**: 智能的字段位置识别算法
- **多线程清理**: 后台自动缓存管理

## 📋 支持的文件格式
- **.xlsx**: Excel 2007+格式
- **.xls**: Excel 97-2003格式
- **最大文件大小**: 16MB

## 🚀 使用方法

### 1. 启动程序
```bash
# Windows
双击 start_server.bat

# macOS/Linux
./start_server.sh
# 或
python3 start_server.py
```

### 2. 访问界面
- 浏览器自动打开 http://localhost:8080
- 或手动访问该地址

### 3. 上传转换
- 拖拽或点击上传Excel文件
- 查看转换进度和统计
- 一键下载结果ZIP包

## 📊 转换示例

### 输入文件结构
- 多个工作表（如：工作表21、工作表18）
- 横向段别数据布局
- 不同工作表字段位置可能不同

### 输出文件结构
- 单个工作表包含所有数据
- 竖向格式：每个段别占一行
- 17个完整字段列
- 来源工作表标记

## 🎯 质量保证
- **数值匹配**: 100%与源文件一致
- **完整性**: 保留所有原始数据
- **可追溯**: 标明数据来源工作表
- **容错性**: 智能处理各种异常情况

## 📞 技术支持
- 程序自动生成详细的转换报告
- 包含处理日志和统计信息
- 支持多工作表批量处理
- 自动缓存清理，无需手动维护

---

**🎉 Excel格式转换工具V2.0 - 更智能、更高效、更可靠！**
