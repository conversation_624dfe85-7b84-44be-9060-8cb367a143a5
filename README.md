# Excel格式转换工具

一个基于Flask的Web应用，用于将Excel文件从一种格式转换为另一种格式。

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动应用

```bash
python start_server.py
```

### 访问应用

在浏览器中打开：<http://localhost:8080>

### 停止应用

```bash
python stop_server.py
```

## 功能特点

- 支持Excel文件上传和下载
- 自动识别多个工作表
- 智能字段映射和数据转换
- 提供详细的转换报告
- 现代化的Web界面
- 拖拽上传支持
- 实时转换进度

## 项目结构

```
FormatConversion/
├── app.py              # 主应用文件
├── start_server.py     # 启动脚本
├── stop_server.py      # 停止脚本（Python版）
├── stop_server.bat     # 停止脚本（Windows批处理）
├── stop_server.sh      # 停止脚本（Unix Shell）
├── requirements.txt    # 依赖列表
├── templates/          # HTML模板
│   └── index.html      # 主页面
├── uploads/            # 上传文件目录
└── outputs/            # 输出文件目录
```

## 技术栈

- **后端**: Python + Flask
- **数据处理**: pandas + openpyxl
- **前端**: HTML5 + CSS3 + JavaScript
- **文件格式**: .xlsx, .xls

## 系统要求

- Python 3.7 或更高版本
- 100MB 可用磁盘空间
- 现代Web浏览器

## 使用说明

1. 启动应用后访问 <http://localhost:8080>
2. 拖拽或点击上传Excel文件
3. 等待转换完成
4. 下载转换后的文件和报告
5. 使用完毕后停止应用

---

**Excel格式转换工具开发团队**
