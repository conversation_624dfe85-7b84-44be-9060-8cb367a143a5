#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel格式转换Web应用
支持文件上传、转换和下载功能
"""

import os
import io
import zipfile
from datetime import datetime
from flask import Flask, render_template, request, send_file, jsonify, flash, redirect, url_for
from werkzeug.utils import secure_filename
import pandas as pd
import numpy as np

app = Flask(__name__)
app.secret_key = 'excel_converter_secret_key_2025'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置上传文件夹
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}

# 确保文件夹存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class ExcelConverter:
    """Excel格式转换器"""
    
    def __init__(self, source_file):
        self.source_file = source_file
        self.conversion_log = []
        self.project_info = {}
        
    def log(self, message):
        """记录转换日志"""
        self.conversion_log.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        
    def extract_project_info(self, df_source):
        """提取项目信息"""
        self.log("开始提取项目信息...")

        project_info = {
            'tower_type': '',
            'height': '',
            'base_qty': 1,
            'tower_no': '',
            'project_no': '',
            'order_no': ''  # 新增订单号字段
        }

        # 搜索项目信息（扩大搜索范围以包含基数字段）
        for i in range(min(10, len(df_source))):
            for j in range(len(df_source.columns)):
                cell_value = df_source.iloc[i, j]
                if pd.notna(cell_value):
                    cell_str = str(cell_value)

                    # 提取项目编号
                    if 'GH' in cell_str and len(cell_str) < 20:
                        project_info['project_no'] = cell_str.strip()
                        self.log(f"找到项目编号: {project_info['project_no']}")

                    # 提取塔型：从"塔型：SJ2\nTOWER TYPE"中提取冒号后英文前的值
                    if '塔型：' in cell_str:
                        if '：' in cell_str:
                            after_colon = cell_str.split('：')[1]
                            if '\n' in after_colon:
                                tower_type = after_colon.split('\n')[0].strip()
                            else:
                                tower_type = after_colon.strip()
                            project_info['tower_type'] = tower_type
                            self.log(f"提取塔型: {tower_type}")

                    # 提取呼高：从"呼高：15\nNominal height"中提取冒号后英文前的值
                    if '呼高：' in cell_str:
                        if '：' in cell_str:
                            after_colon = cell_str.split('：')[1]
                            if '\n' in after_colon:
                                height = after_colon.split('\n')[0].strip()
                            else:
                                height = after_colon.strip()
                            project_info['height'] = height
                            self.log(f"提取呼高: {height}")

                    # 提取塔号：从"塔号:\nTOWER NO."中提取冒号后英文前的值
                    if '塔号:' in cell_str or '塔号：' in cell_str:
                        if ':' in cell_str:
                            after_colon = cell_str.split(':')[1]
                        elif '：' in cell_str:
                            after_colon = cell_str.split('：')[1]
                        else:
                            after_colon = ''

                        if after_colon:
                            if '\n' in after_colon:
                                tower_no = after_colon.split('\n')[0].strip()
                            else:
                                tower_no = after_colon.strip()
                            # 如果提取到的值不为空且不是纯英文，则使用
                            if tower_no and not tower_no.replace(' ', '').isalpha():
                                project_info['tower_no'] = tower_no
                                self.log(f"提取塔号: {tower_no}")
                            else:
                                project_info['tower_no'] = ''
                                self.log("塔号为空")

                    # 动态提取基数：从"基数：\nTOWER QTY"字段右边4个单元格的合并单元格中读取
                    if '基数：' in cell_str and 'TOWER QTY' in cell_str:
                        self.log(f"找到基数字段位置: 行{i}列{j}")
                        # 查找右边4个单元格的合并单元格中的值
                        base_qty_value = self.extract_base_qty_value(df_source, i, j)
                        if base_qty_value:
                            project_info['base_qty'] = base_qty_value
                            self.log(f"提取基数: {base_qty_value}")
                        else:
                            self.log("基数字段右边4个单元格中未找到有效值，使用默认值1")

                    # 动态提取订单号：从"订单号/ORDER NO.：GH25019A"中提取冒号后的值
                    if '订单号' in cell_str and 'ORDER NO' in cell_str and '：' in cell_str:
                        self.log(f"找到订单号字段位置: 行{i}列{j}")
                        # 提取冒号后的值
                        after_colon = cell_str.split('：')[1]
                        if '\n' in after_colon:
                            order_no = after_colon.split('\n')[0].strip()
                        else:
                            order_no = after_colon.strip()
                        if order_no:
                            project_info['order_no'] = order_no
                            self.log(f"提取订单号: {order_no}")
                        else:
                            self.log("订单号字段冒号后未找到有效值")

        # 设置默认值
        if not project_info['tower_type']:
            project_info['tower_type'] = 'SJ2'
        if not project_info['height']:
            project_info['height'] = '15'

        self.project_info = project_info
        return project_info

    def extract_base_qty_value(self, df_source, base_row, base_col):
        """从基数字段右边4个单元格的合并单元格中提取基数值"""
        try:
            # 检查右边4个单元格（base_col+1 到 base_col+4）
            for offset in range(1, 5):
                target_col = base_col + offset
                if target_col < len(df_source.columns):
                    cell_value = df_source.iloc[base_row, target_col]
                    if pd.notna(cell_value):
                        cell_str = str(cell_value).strip()
                        # 尝试转换为数字
                        try:
                            base_qty = int(float(cell_str))
                            if base_qty > 0:
                                self.log(f"在列{target_col}找到基数值: {base_qty}")
                                return base_qty
                        except (ValueError, TypeError):
                            # 如果不是数字，继续检查下一个单元格
                            continue

            # 如果右边4个单元格都没有找到有效值，检查同行其他单元格
            for col in range(len(df_source.columns)):
                if col != base_col:  # 跳过基数字段本身
                    cell_value = df_source.iloc[base_row, col]
                    if pd.notna(cell_value):
                        cell_str = str(cell_value).strip()
                        try:
                            base_qty = int(float(cell_str))
                            if base_qty > 0 and base_qty < 100:  # 基数通常是小于100的正整数
                                self.log(f"在同行列{col}找到基数值: {base_qty}")
                                return base_qty
                        except (ValueError, TypeError):
                            continue

            return None

        except Exception as e:
            self.log(f"提取基数值时出错: {e}")
            return None

    def find_data_start_row(self, df_source):
        """查找数据开始行"""
        self.log("查找数据开始行...")

        for i in range(len(df_source)):
            for j in range(len(df_source.columns)):
                cell_value = str(df_source.iloc[i, j])
                if ('NO.' in cell_value.upper() and '序号' in cell_value) or cell_value.strip() == '序号':
                    self.log(f"找到数据开始行: {i}")
                    return i

        self.log("未找到明确的数据开始行，使用默认值: 4")
        return 4
    
    def extract_segment_headers(self, df_source, data_start_row):
        """提取段别标题"""
        self.log("提取段别标题...")
        
        segment_headers = []
        header_row = data_start_row + 1
        
        if header_row < len(df_source):
            for j in range(6, min(20, len(df_source.columns))):
                cell_value = df_source.iloc[header_row, j]
                if pd.notna(cell_value):
                    # 处理数字类型和字符串类型
                    if isinstance(cell_value, (int, float)) and not pd.isna(cell_value):
                        segment_headers.append(int(cell_value))
                    else:
                        cell_str = str(cell_value).strip()
                        if cell_str.isdigit():
                            segment_headers.append(int(cell_str))
        
        if not segment_headers:
            segment_headers = [1, 2, 3, 4, 5, 6, 7, 11]
            
        self.log(f"段别标题: {segment_headers}")
        return segment_headers

    def find_field_columns(self, df_source):
        """动态查找关键字段的列位置"""
        self.log("查找关键字段列位置...")

        field_columns = {
            'extra_5_percent': None,  # 加5% EXTRA 5%
            'total_amount': None,     # 单基总数 TOTAL AMOUNT/TOWER
            'qty_tower': None,        # 单基 QTY/TOWER
            'total_quantity': None,   # 总数 TOTAL QUANTITY
            'remarks': None           # 备注 REMARKS
        }

        # 在前10行中查找字段标题
        for i in range(min(10, len(df_source))):
            for j in range(len(df_source.columns)):
                cell_value = str(df_source.iloc[i, j])

                # 查找加5% EXTRA 5%列
                if '加5%' in cell_value and 'EXTRA' in cell_value:
                    field_columns['extra_5_percent'] = j
                    self.log(f"找到加5%列: 列{j}")

                # 查找单基总数 TOTAL AMOUNT/TOWER列
                if '单基总数' in cell_value and 'TOTAL AMOUNT' in cell_value:
                    field_columns['total_amount'] = j
                    self.log(f"找到单基总数列: 列{j}")

                # 查找单基 QTY/TOWER列
                if '单基' in cell_value and 'QTY' in cell_value and 'TOWER' in cell_value and '总数' not in cell_value:
                    field_columns['qty_tower'] = j
                    self.log(f"找到单基QTY/TOWER列: 列{j}")

                # 查找总数 TOTAL QUANTITY列
                if '总数' in cell_value and 'TOTAL QUANTITY' in cell_value:
                    field_columns['total_quantity'] = j
                    self.log(f"找到总数TOTAL QUANTITY列: 列{j}")

                # 查找备注 REMARKS列
                if '备注' in cell_value and 'REMARKS' in cell_value:
                    field_columns['remarks'] = j
                    self.log(f"找到备注列: 列{j}")

        # 设置默认值（如果没找到）
        if field_columns['extra_5_percent'] is None:
            field_columns['extra_5_percent'] = 22
            self.log("未找到加5%列，使用默认值: 列22")

        if field_columns['total_amount'] is None:
            field_columns['total_amount'] = 23
            self.log("未找到单基总数列，使用默认值: 列23")

        if field_columns['qty_tower'] is None:
            field_columns['qty_tower'] = 21
            self.log("未找到单基QTY/TOWER列，使用默认值: 列21")

        if field_columns['total_quantity'] is None:
            field_columns['total_quantity'] = 25
            self.log("未找到总数TOTAL QUANTITY列，使用默认值: 列25")

        if field_columns['remarks'] is None:
            field_columns['remarks'] = 26
            self.log("未找到备注列，使用默认值: 列26")

        return field_columns

    def convert_to_vertical_format(self, output_file):
        """转换为竖向格式"""
        try:
            self.log("开始读取源文件...")

            # 获取所有sheet页名称
            xl_file = pd.ExcelFile(self.source_file)
            sheet_names = xl_file.sheet_names
            self.log(f"发现 {len(sheet_names)} 个工作表: {sheet_names}")

            all_converted_data = []
            processed_sheets = 0
            
            # 循环处理每个工作表
            for sheet_name in sheet_names:
                try:
                    self.log(f"正在处理工作表: {sheet_name}")
                    df_source = pd.read_excel(self.source_file, sheet_name=sheet_name, header=None)

                    # 检查工作表是否包含有效数据
                    if df_source.empty or df_source.shape[0] < 5:
                        self.log(f"跳过工作表 {sheet_name}: 数据不足")
                        continue

                    # 提取项目信息
                    project_info = self.extract_project_info(df_source)

                    # 查找数据开始行
                    data_start_row = self.find_data_start_row(df_source)

                    # 提取段别标题
                    segment_headers = self.extract_segment_headers(df_source, data_start_row)

                    # 动态查找关键字段的列位置
                    field_columns = self.find_field_columns(df_source)

                    # 提取数据行
                    sheet_converted_data = []
                    for i in range(data_start_row + 2, len(df_source)):
                        row_data = []
                        for j in range(len(df_source.columns)):
                            cell_value = df_source.iloc[i, j]
                            row_data.append(cell_value)
                
                        # 检查是否为有效数据行
                        if len(row_data) > 1 and pd.notna(row_data[1]):
                            # 提取基本信息
                            material_name = str(row_data[1]) if pd.notna(row_data[1]) else ''
                            specification = str(row_data[2]) if pd.notna(row_data[2]) else ''
                            grade = str(row_data[4]) if pd.notna(row_data[4]) else ''
                            no_buckle_length = str(row_data[5]) if pd.notna(row_data[5]) else ''

                            # 使用动态查找的列位置提取备注信息
                            remarks = ''
                            remarks_col = field_columns['remarks']
                            if len(row_data) > remarks_col and pd.notna(row_data[remarks_col]):
                                remarks = str(row_data[remarks_col]).strip()
                                # 保留完整的备注信息，不进行截取

                            # 使用动态查找的列位置提取加5% EXTRA 5%的值
                            extra_5_percent = 0
                            extra_col = field_columns['extra_5_percent']
                            if len(row_data) > extra_col and pd.notna(row_data[extra_col]):
                                try:
                                    extra_5_percent = int(float(row_data[extra_col]))
                                except (ValueError, TypeError):
                                    extra_5_percent = 0

                            # 使用动态查找的列位置提取单基总数 TOTAL AMOUNT/TOWER的值
                            total_amount_value = 0
                            total_col = field_columns['total_amount']
                            if len(row_data) > total_col and pd.notna(row_data[total_col]):
                                try:
                                    total_amount_value = int(float(row_data[total_col]))
                                except (ValueError, TypeError):
                                    total_amount_value = 0

                            # 使用动态查找的列位置提取单基 QTY/TOWER的值
                            qty_tower_value = 0
                            qty_tower_col = field_columns['qty_tower']
                            if len(row_data) > qty_tower_col and pd.notna(row_data[qty_tower_col]):
                                try:
                                    qty_tower_value = int(float(row_data[qty_tower_col]))
                                except (ValueError, TypeError):
                                    qty_tower_value = 0

                            # 使用动态查找的列位置提取总数 TOTAL QUANTITY的值
                            total_quantity_value = 0
                            total_quantity_col = field_columns['total_quantity']
                            if len(row_data) > total_quantity_col and pd.notna(row_data[total_quantity_col]):
                                try:
                                    total_quantity_value = int(float(row_data[total_quantity_col]))
                                except (ValueError, TypeError):
                                    total_quantity_value = 0
                    
                            # 提取段别数据
                            segment_data = []
                            for idx, segment in enumerate(segment_headers):
                                col_idx = 6 + idx
                                if col_idx < len(row_data) and pd.notna(row_data[col_idx]):
                                    try:
                                        quantity = int(float(row_data[col_idx]))
                                        if quantity > 0:
                                            segment_data.append({
                                                'segment': segment,
                                                'quantity': quantity
                                            })
                                    except (ValueError, TypeError):
                                        continue

                            # 计算总数量
                            if segment_data:  # 只有当有段别数据时才处理
                                total_quantity = sum(seg['quantity'] for seg in segment_data)

                                # 为每个段别创建一行记录（竖向格式）
                                for seg_data in segment_data:
                                    converted_row = {
                                        '物料名称': material_name,
                                        '物料编码': '',
                                        '塔型': project_info['tower_type'],
                                        '塔号': project_info['tower_no'],
                                        '呼高': project_info['height'],
                                        '基数': project_info['base_qty'],
                                        '订单号': project_info['order_no'],  # 新增订单号列
                                        '规格': specification,
                                        '等级': grade,
                                        '备注': remarks,
                                        '无扣长': no_buckle_length,
                                        '段别': seg_data['segment'],
                                        '单基数量': seg_data['quantity'],
                                        '单基 QTY/TOWER': qty_tower_value,  # 使用从源文件提取的值
                                        '单基总数': total_amount_value,  # 新增列：单基总数 TOTAL AMOUNT/TOWER
                                        '加5% EXTRA 5%': extra_5_percent,  # 使用从源文件提取的值
                                        '总数': total_quantity_value,  # 总数 TOTAL QUANTITY
                                        '来源工作表': sheet_name
                                    }
                                    sheet_converted_data.append(converted_row)
            
                    # 添加当前工作表的数据到总数据中
                    all_converted_data.extend(sheet_converted_data)
                    processed_sheets += 1
                    self.log(f"工作表 {sheet_name} 处理完成，提取到 {len(sheet_converted_data)} 行数据")

                except Exception as e:
                    self.log(f"处理工作表 {sheet_name} 时出错: {str(e)}")
                    continue

            self.log(f"总共处理了 {processed_sheets} 个工作表，提取到 {len(all_converted_data)} 行数据")

            if not all_converted_data:
                raise Exception("没有找到有效的数据进行转换")

            # 创建DataFrame
            df_converted = pd.DataFrame(all_converted_data)
            
            # 保存到Excel文件
            self.log("正在保存到目标文件...")
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                df_converted.to_excel(writer, sheet_name='sheet1', index=False)
                
                # 创建格式规范sheet
                format_data = {
                    '字段名': ['物料名称', '物料编码', '塔型', '塔号', '呼高', '基数', '订单号', '规格', '等级', '备注', '无扣长', '段别', '单基数量'],
                    '说明': ['从品名字段提取', '保持为空', '自动提取', '源文件为空则保持为空', '自动提取', '基数字段', '自动提取', '规格字段', '等级字段', '备注字段', '无扣长字段', '横向转竖向', '横向转竖向']
                }
                df_format = pd.DataFrame(format_data)
                df_format.to_excel(writer, sheet_name='字段导入格式规范', index=False)
            
            self.log(f"数据已保存到 {output_file}")
            self.log(f"共保存 {len(all_converted_data)} 行数据")

            return {
                'success': True,
                'records': len(all_converted_data),
                'processed_sheets': processed_sheets,
                'sheet_names': sheet_names,
                'project_info': project_info,
                'material_types': int(df_converted['物料名称'].nunique()),
                'spec_types': int(df_converted['规格'].nunique()),
                'total_quantity': int(df_converted.groupby(['物料名称', '规格'])['单基 QTY/TOWER'].first().sum())
            }
            
        except Exception as e:
            self.log(f"转换过程中出现错误: {str(e)}")
            return {'success': False, 'error': str(e)}

def generate_conversion_report(conversion_result, log_messages):
    """生成转换报告"""
    report_content = f"""
# Excel格式转换报告

## 转换时间
{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

## 转换结果
- 状态: {'✅ 成功' if conversion_result['success'] else '❌ 失败'}
- 转换记录数: {conversion_result.get('records', 0)}
- 处理工作表数: {conversion_result.get('processed_sheets', 0)}
- 物料种类: {conversion_result.get('material_types', 0)}
- 规格种类: {conversion_result.get('spec_types', 0)}
- 总数量: {conversion_result.get('total_quantity', 0)}

## 工作表信息
- 发现的工作表: {', '.join(conversion_result.get('sheet_names', []))}
- 成功处理: {conversion_result.get('processed_sheets', 0)} 个工作表
- 跳过的工作表: {len(conversion_result.get('sheet_names', [])) - conversion_result.get('processed_sheets', 0)} 个

## 项目信息
- 塔型: {conversion_result.get('project_info', {}).get('tower_type', 'N/A')}
- 呼高: {conversion_result.get('project_info', {}).get('height', 'N/A')}
- 基数: {conversion_result.get('project_info', {}).get('base_qty', 'N/A')}
- 订单号: {conversion_result.get('project_info', {}).get('order_no', 'N/A')}
- 项目编号: {conversion_result.get('project_info', {}).get('project_no', 'N/A')}

## 转换日志
"""
    
    for log_msg in log_messages:
        report_content += f"- {log_msg}\n"
    
    return report_content

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传和转换"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'error': '没有选择文件'})
    
    if file and allowed_file(file.filename):
        # 保存上传的文件
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        source_file = os.path.join(UPLOAD_FOLDER, f"{timestamp}_{filename}")
        file.save(source_file)

        # 生成输出文件名：原文件名+转换后
        base_name = os.path.splitext(filename)[0]  # 去掉扩展名
        output_filename = f"{base_name}_转换后.xlsx"

        # 执行转换
        converter = ExcelConverter(source_file)
        output_file = os.path.join(OUTPUT_FOLDER, f"{timestamp}_converted.xlsx")

        conversion_result = converter.convert_to_vertical_format(output_file)
        
        if conversion_result['success']:
            # 生成报告
            report_content = generate_conversion_report(conversion_result, converter.conversion_log)
            report_file = os.path.join(OUTPUT_FOLDER, f"{timestamp}_report.md")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            # 创建下载包
            zip_file = os.path.join(OUTPUT_FOLDER, f"{timestamp}_package.zip")
            with zipfile.ZipFile(zip_file, 'w') as zf:
                zf.write(output_file, output_filename)  # 使用原文件名+转换后
                zf.write(report_file, '转换报告.md')
            
            return jsonify({
                'success': True,
                'download_url': f'/download/{timestamp}_package.zip',
                'result': conversion_result
            })
        else:
            return jsonify({
                'success': False,
                'error': conversion_result.get('error', '转换失败')
            })
    
    return jsonify({'success': False, 'error': '不支持的文件格式'})

@app.route('/download/<filename>')
def download_file(filename):
    """下载文件并清空缓存"""
    file_path = os.path.join(OUTPUT_FOLDER, filename)
    if os.path.exists(file_path):
        # 获取时间戳前缀用于后续清理
        timestamp_prefix = filename.split('_package.zip')[0]

        # 发送文件
        response = send_file(file_path, as_attachment=True, download_name=filename)

        # 在后台线程中延迟清理文件
        import threading
        import time

        def delayed_cleanup():
            time.sleep(2)  # 等待2秒确保下载完成
            try:
                cleanup_cache_files(timestamp_prefix)
            except Exception as e:
                print(f"清理缓存文件时出错: {e}")

        # 启动后台清理线程
        cleanup_thread = threading.Thread(target=delayed_cleanup)
        cleanup_thread.daemon = True
        cleanup_thread.start()

        return response
    else:
        return "文件不存在", 404

def cleanup_cache_files(timestamp_prefix):
    """清理指定时间戳的缓存文件"""
    import shutil

    try:
        # 清理uploads文件夹中的文件
        for filename in os.listdir(UPLOAD_FOLDER):
            if filename.startswith(timestamp_prefix):
                file_path = os.path.join(UPLOAD_FOLDER, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"已删除上传文件: {filename}")
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"已删除上传目录: {filename}")

        # 清理outputs文件夹中的文件和目录
        for filename in os.listdir(OUTPUT_FOLDER):
            if filename.startswith(timestamp_prefix):
                file_path = os.path.join(OUTPUT_FOLDER, filename)
                if os.path.isfile(file_path):
                    os.remove(file_path)
                    print(f"已删除输出文件: {filename}")
                elif os.path.isdir(file_path):
                    shutil.rmtree(file_path)
                    print(f"已删除输出目录: {filename}")

        print(f"缓存清理完成: {timestamp_prefix}")

    except Exception as e:
        print(f"清理缓存文件时出错: {e}")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=8080)
